"""
Test script to demonstrate format-preserving position mapping
"""

from utils.error_position_mapper import AdvancedPositionMapper

def test_format_preservation():
    """Test that format preservation maintains accurate character positions"""
    
    # Example SQL with various formatting
    original_sql = """CREATE OR REPLACE FUNCTION test_func()
RETURNS INTEGER
AS
$$
BEGIN
    SELECT invalid_column FROM table;
    RETURN 1;
END;
$$
LANGUAGE plpgsql;

-- Another statement
INSERT INTO test_table (col1, col2) 
VALUES (1, 'test');"""

    print("🔍 Testing Format-Preserving Position Mapping")
    print("=" * 50)
    
    # Create position mapper
    mapper = AdvancedPositionMapper()
    statements, position_mapper = mapper.split_with_comprehensive_mapping(original_sql)
    
    print(f"📊 Original SQL length: {len(original_sql)} characters")
    print(f"📊 Number of statements: {len(statements)}")
    print()
    
    # Show each statement with its preserved format
    for i, stmt in enumerate(statements, 1):
        print(f"Statement {i}:")
        print(f"Length: {len(stmt)} characters")
        print(f"Content: {repr(stmt[:100])}...")
        print()
    
    # Test position mapping
    print("🎯 Testing Position Mapping:")
    
    # Find position of "invalid_column" in original SQL
    error_pos = original_sql.find("invalid_column")
    print(f"Position of 'invalid_column': {error_pos}")
    
    if error_pos in position_mapper.position_to_statement:
        stmt_num = position_mapper.position_to_statement[error_pos]
        print(f"✅ Maps to statement #{stmt_num}")
        print(f"Statement content: {repr(statements[stmt_num-1][:50])}...")
    else:
        print("❌ Position not found in mapping")
    
    # Test line mapping
    error_line = original_sql[:error_pos].count('\n') + 1
    print(f"Line number of 'invalid_column': {error_line}")
    
    if error_line in position_mapper.line_to_statement:
        stmt_num = position_mapper.line_to_statement[error_line]
        print(f"✅ Line maps to statement #{stmt_num}")
    else:
        print("❌ Line not found in mapping")

if __name__ == "__main__":
    test_format_preservation()
