"""
Demo script showing format-preserving position mapping
"""

from formatting.sql_splitter import split_sql_statements
from utils.error_position_mapper import AdvancedPositionMapper

def demo_format_preservation():
    """Demonstrate format preservation vs regular splitting"""
    
    # Example SQL with various formatting
    original_sql = """CREATE OR REPLACE FUNCTION test_func()    
RETURNS INTEGER                      
AS                                   
$$                                   
BEGIN                                
    SELECT invalid_column FROM table;
    RETURN 1;                        
END;                                 
$$                                   
LANGUAGE plpgsql;

-- Another statement with formatting
INSERT INTO test_table (col1, col2) 
VALUES (1, 'test');"""

    print("🔍 Format Preservation Demo")
    print("=" * 60)
    print(f"Original SQL ({len(original_sql)} chars):")
    print(repr(original_sql))
    print()
    
    # Test regular splitting (old behavior)
    print("📊 Regular Splitting (strips formatting):")
    regular_statements = split_sql_statements(original_sql, preserve_formatting=False)
    for i, stmt in enumerate(regular_statements, 1):
        print(f"Statement {i} ({len(stmt)} chars): {repr(stmt[:50])}...")
    print()
    
    # Test format-preserving splitting
    print("✨ Format-Preserving Splitting:")
    preserved_statements = split_sql_statements(original_sql, preserve_formatting=True)
    for i, stmt in enumerate(preserved_statements, 1):
        print(f"Statement {i} ({len(stmt)} chars): {repr(stmt[:50])}...")
    print()
    
    # Test position mapping
    print("🎯 Position Mapping Test:")
    mapper = AdvancedPositionMapper()
    statements, position_mapper = mapper.split_with_comprehensive_mapping(original_sql)
    
    # Find position of "invalid_column"
    error_pos = original_sql.find("invalid_column")
    print(f"Position of 'invalid_column': {error_pos}")
    
    if error_pos in position_mapper.position_to_statement:
        stmt_num = position_mapper.position_to_statement[error_pos]
        print(f"✅ Maps to statement #{stmt_num}")
        print(f"Statement: {repr(statements[stmt_num-1][:60])}...")
    else:
        print("❌ Position not found in mapping")
    
    # Test line mapping
    error_line = original_sql[:error_pos].count('\n') + 1
    print(f"Line number of 'invalid_column': {error_line}")
    
    if error_line in position_mapper.line_to_statement:
        stmt_num = position_mapper.line_to_statement[error_line]
        print(f"✅ Line maps to statement #{stmt_num}")
    else:
        print("❌ Line not found in mapping")

if __name__ == "__main__":
    demo_format_preservation()
