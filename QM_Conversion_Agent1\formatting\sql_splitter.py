import re
from typing import List


def merge_postgresql_end_patterns(statements: List[str]) -> List[str]:
    """
    Merge PostgreSQL 'end;' patterns with following dollar-quoted strings.

    Handles patterns like:
    - end;$BODY$;
    - end objectname;$$;
    - end objectname;$customname$;

    These can be on same line, different lines, or separated by empty lines.

    Args:
        statements: List of SQL statements to process

    Returns:
        List of merged SQL statements
    """
    if not statements:
        return statements

    merged = []
    i = 0

    while i < len(statements):
        current_stmt = statements[i].strip()

        # Check if current statement ends with 'end;' pattern (with optional object name)
        if is_postgresql_end_pattern(current_stmt):
            # Look ahead for dollar-quoted string in subsequent statements
            merged_stmt = current_stmt
            j = i + 1

            # Look through subsequent statements (including empty ones)
            while j < len(statements):
                next_stmt = statements[j].strip()

                # If we find a dollar-quoted pattern, merge it
                if is_dollar_quoted_pattern(next_stmt):
                    # Merge with appropriate spacing
                    if current_stmt.endswith(';') and next_stmt.startswith('$'):
                        merged_stmt = merged_stmt + next_stmt  # Direct concatenation
                    else:
                        merged_stmt = merged_stmt + '\n' + next_stmt

                    # Skip the merged statement
                    i = j + 1
                    break
                elif next_stmt == "":
                    # Skip empty statements and continue looking
                    j += 1
                else:
                    # Found non-empty, non-dollar-quoted statement - stop looking
                    i += 1
                    break
            else:
                # Reached end of statements without finding dollar-quoted pattern
                i += 1

            merged.append(merged_stmt)
        else:
            # Regular statement - add as-is
            merged.append(current_stmt)
            i += 1

    return merged


def is_postgresql_end_pattern(statement: str) -> bool:
    """
    Check if statement matches PostgreSQL 'end;' pattern.

    Patterns:
    - end;
    - end objectname;
    - END;
    - END ObjectName;
    - end   ; (with whitespace)
    """
    if not statement:
        return False

    # Remove extra whitespace and normalize
    stmt = statement.strip()

    # Pattern: end [optional_name] [optional_whitespace] ;
    pattern = r'\bend\s*(?:\w+\s*)?\s*;$'
    return bool(re.search(pattern, stmt, re.IGNORECASE))


def is_dollar_quoted_pattern(statement: str) -> bool:
    """
    Check if statement is a dollar-quoted string pattern.

    Patterns:
    - $BODY$;
    - $$;
    - $customname$;
    - $BODY$   ; (with whitespace)
    """
    if not statement:
        return False

    stmt = statement.strip()

    # Pattern: $[optional_name]$ [optional_whitespace] [optional_semicolon]
    pattern = r'^\$\w*\$\s*;?\s*$'
    return bool(re.match(pattern, stmt))


def split_sql_statements(sql_code: str, preserve_formatting: bool = False) -> List[str]:
    """
    Split SQL code into individual statements while preserving logical constructs.

    Universal Rules:
    1. Split on first occurrence of 'AS' or 'IS' keywords (using word boundaries)
    2. Split on semicolons that are not within comments or quotes
    3. Preserve multi-line logical constructs as single statements
    4. Do not split within SQL comments (/* */ or --)
    5. Do not split if semicolon is within single quotes
    6. Preserve case of the original SQL
    7. Maintain structural integrity of code blocks
    8. PostgreSQL-specific: Merge 'end;' patterns with following dollar-quoted strings

    Args:
        sql_code (str): The SQL code to split
        preserve_formatting (bool): If True, preserves original whitespace and formatting

    Returns:
        List[str]: List of individual SQL statements with preserved structure
    """
    if not sql_code or not sql_code.strip():
        return []

    statements = []

    # Function to check if position is within a comment
    def is_in_comment(pos: int, text: str) -> bool:
        if pos >= len(text):
            return False

        # More efficient implementation for checking if position is within a comment
        # Check for block comments /* */
        block_comment_pairs = []
        block_starts = [m.start() for m in re.finditer(r'/\*', text[:pos+1])]
        if block_starts:
            # Only find ends that could potentially contain our position
            block_ends = [m.start() + 2 for m in re.finditer(r'\*/', text)]

            # Pair up start and end positions
            for start in block_starts:
                # Find the first end that comes after this start
                for end in block_ends:
                    if end > start:
                        block_comment_pairs.append((start, end))
                        break

        # Check if position is within any block comment
        for start, end in block_comment_pairs:
            if start < pos < end:
                return True

        # Check for line comments --
        line_starts = [m.start() for m in re.finditer(r'--', text[:pos+1])]
        for start in line_starts:
            line_end = text.find('\n', start)
            if line_end == -1:  # Comment goes to end of string
                line_end = len(text)
            if start < pos < line_end:
                return True

        return False

    # Function to check if position is within quotes
    def is_in_quotes(pos: int, text: str) -> bool:
        # More efficient implementation using regex to find all single quotes
        # and then determining if the position is within quotes
        if pos >= len(text):
            return False

        # Find all single quotes before the position
        quote_positions = [m.start() for m in re.finditer(r"'", text[:pos])]

        # Handle escaped quotes (two consecutive single quotes)
        # by removing pairs of consecutive quotes
        i = 0
        while i < len(quote_positions) - 1:
            if quote_positions[i+1] - quote_positions[i] == 1:  # Consecutive quotes
                quote_positions.pop(i)
                quote_positions.pop(i)
                # Don't increment i since we removed two elements
            else:
                i += 1

        # If there's an odd number of quotes, we're inside quotes
        return len(quote_positions) % 2 == 1



    # Process the SQL code
    remaining_code = sql_code.strip() if not preserve_formatting else sql_code

    # First, try to split on AS or IS keywords with word boundaries
    as_is_pattern = r'\b(AS|IS)\b'

    # Process the first occurrence of AS or IS
    # Find the first AS or IS with word boundaries
    match = re.search(as_is_pattern, remaining_code, re.IGNORECASE)

    if match and not is_in_comment(match.start(), remaining_code) and not is_in_quotes(match.start(), remaining_code):
        # Split before the match (don't include AS or IS)
        first_part = remaining_code[:match.start()]
        if preserve_formatting:
            statements.append(first_part)  # Keep original formatting
        else:
            statements.append(first_part.strip())  # Original behavior
        # Keep the AS or IS with the second part
        remaining_code = remaining_code[match.start():].strip() if not preserve_formatting else remaining_code[match.start():]

    # If we still have code to process, split by semicolons only
    if remaining_code:
        # Find all semicolons
        semicolon_positions = [m.start() for m in re.finditer(';', remaining_code)]

        if not semicolon_positions:
            # No semicolons found, add the remaining code as a single statement
            if preserve_formatting:
                statements.append(remaining_code)  # Keep original formatting
            else:
                statements.append(remaining_code.strip())  # Original behavior
        else:
            last_pos = 0
            for pos in semicolon_positions:
                # Only split if semicolon is not in a comment or quotes
                if not is_in_comment(pos, remaining_code) and not is_in_quotes(pos, remaining_code):
                    if preserve_formatting:
                        statements.append(remaining_code[last_pos:pos+1])  # Keep original formatting
                    else:
                        statements.append(remaining_code[last_pos:pos+1].strip())  # Original behavior
                    last_pos = pos + 1

            # Add any remaining code after the last semicolon
            if last_pos < len(remaining_code):
                if preserve_formatting:
                    statements.append(remaining_code[last_pos:])  # Keep original formatting
                else:
                    statements.append(remaining_code[last_pos:].strip())  # Original behavior

    # Filter out empty statements (but be more careful with formatting preservation)
    if preserve_formatting:
        filtered_statements = [stmt for stmt in statements if stmt.strip()]  # Only remove truly empty statements
    else:
        filtered_statements = [stmt for stmt in statements if stmt.strip()]  # Original behavior

    # PostgreSQL-specific: Merge 'end;' patterns with following dollar-quoted strings
    merged_statements = merge_postgresql_end_patterns(filtered_statements)

    return merged_statements
